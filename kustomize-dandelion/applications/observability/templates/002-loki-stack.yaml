{{- if .Values.dandelion.logging.enabled -}}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: loki
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: dandelion-observability
  source:
    repoURL: https://grafana.github.io/helm-charts
    chart: loki-stack
    targetRevision: 2.4.1
    helm:
      parameters:
        # This could be used to test dev branches.
        - name: "git.targetRevision"
          value: {{ .Values.git.targetRevision | default "HEAD" }}
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
  destination:
    namespace: observe
    server: {{ .Values.spec.destination.server }}

{{- end }}
