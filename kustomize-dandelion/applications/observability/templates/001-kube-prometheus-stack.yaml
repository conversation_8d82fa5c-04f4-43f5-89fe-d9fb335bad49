{{- if .Values.dandelion.monitoring.enabled -}}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: kube-prometheus
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: dandelion-observability
  source:
    repoURL: https://prometheus-community.github.io/helm-charts
    chart: kube-prometheus-stack
    targetRevision: 16.0.1
    helm:
      parameters:
        # This could be used to test dev branches.
        - name: "git.targetRevision"
          value: {{ .Values.git.targetRevision | default "HEAD" }}
        - name: "grafana.ingress.enabled"
          value: "true"
        - name: "grafana.ingress.hosts[0]"
          value: "grafana.local"
      values: |
        ## Config coming from values file depending on the k8s deployment
        {{ with .Values.kubePrometheusStack }}
{{ toYaml . | indent 8 }}
        {{ end }}

  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
  destination:
    namespace: observe
    server: {{ .Values.spec.destination.server }}

{{- end }}
