{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Traefik dashboard prometheus", "editable": true, "gnetId": 4475, "graphTooltip": 0, "id": 22, "iteration": 1595773597259, "links": [], "panels": [{"datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 10, "title": "$backend stats", "type": "row"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#d44a3a", "rgba(237, 129, 40, 0.89)", "#299c46"], "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 1}, "id": 1, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum(traefik_backend_server_up{backend=\"$backend\"})/count(traefik_config_reloads_total)", "format": "time_series", "intervalFactor": 2, "refId": "A"}], "thresholds": "0,1", "title": "$backend status", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "OK", "value": "1"}], "valueName": "current"}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "80%", "format": "short", "gridPos": {"h": 7, "w": 8, "x": 8, "y": 1}, "id": 2, "interval": null, "legend": {"percentage": true, "show": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 3, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": "traefik_backend_requests_total{backend=\"$backend\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{method}} : {{code}}", "refId": "A"}], "title": "$backend return code", "type": "grafana-piechart-panel", "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "ms", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 1}, "id": 4, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "sum(traefik_backend_request_duration_seconds_sum{backend=\"$backend\"}) / sum(traefik_backend_requests_total{backend=\"$backend\"}) * 1000", "format": "time_series", "intervalFactor": 2, "refId": "A"}], "thresholds": "", "title": "$backend response time", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 8}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(traefik_backend_requests_total{backend=\"$backend\"}[5m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "Total requests $backend", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total requests over 5min $backend", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 12, "panels": [], "title": "Global stats", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "rate(traefik_entrypoint_requests_total{entrypoint=~\"$entrypoint\",code=\"200\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{method}} : {{code}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Status code 200 over 5min", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 16}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "rate(traefik_entrypoint_requests_total{entrypoint=~\"$entrypoint\",code!=\"200\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ method }} : {{code}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Others status code over 5min", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "80%", "format": "short", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 23}, "id": 7, "interval": null, "legend": {"show": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 3, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": "sum(rate(traefik_backend_requests_total[5m])) by (backend) ", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ backend }}", "refId": "A"}], "title": "Requests by service", "type": "grafana-piechart-panel", "valueName": "total"}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "80%", "format": "short", "gridPos": {"h": 7, "w": 12, "x": 12, "y": 23}, "id": 8, "interval": null, "legend": {"show": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 3, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": "sum(rate(traefik_entrypoint_requests_total{entrypoint =~ \"$entrypoint\"}[5m])) by (entrypoint) ", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ entrypoint }}", "refId": "A"}], "title": "Requests by protocol", "type": "grafana-piechart-panel", "valueName": "total"}], "schemaVersion": 25, "style": "dark", "tags": ["traefik", "prometheus"], "templating": {"list": [{"allValue": null, "current": {"selected": true, "text": "test.cablespaghetti.dev/", "value": "test.cablespaghetti.dev/"}, "datasource": "Prometheus", "definition": "", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "backend", "options": [], "query": "label_values(backend)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "tags": [], "text": "All + http + https", "value": ["$__all", "http", "https"]}, "datasource": "Prometheus", "definition": "", "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "entrypoint", "options": [], "query": "label_values(entrypoint)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Traefik", "uid": "qPdAviJmz", "version": 1}