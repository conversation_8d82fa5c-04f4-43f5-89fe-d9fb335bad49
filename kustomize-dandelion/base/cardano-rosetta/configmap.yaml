apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-rosetta-configmap
data:
  ecosystem.config.js: |
    module.exports = {
      apps : [
        {
          name: 'cardano-rosetta-server',
          script: '/cardano-rosetta-server/dist/src/server/index.js',
          autorestart: true,
          env: {
            BIND_ADDRESS: process.env.BIND_ADDRESS,
            CARDANO_CLI_PATH: "/usr/local/bin/cardano-cli",
            CARDANO_NODE_PATH: "/usr/local/bin/cardano-node",
            CARDANO_NODE_SOCKET_PATH: process.env.CARDANO_NODE_SOCKET_PATH,
            DB_CONNECTION_STRING: "postgresql://"+process.env.POSTGRES_USER+":"+process.env.POSTGRES_PASS+"@"+process.env.POSTGRES_HOST+":"+process.env.POSTGRES_PORT+"/db="+process.env.POSTGRES_DB,
            DEFAULT_RELATIVE_TTL: process.env.DEFAULT_RELATIVE_TTL,
            GENESIS_SHELLEY_PATH: process.env.GENESIS_SHELLEY_PATH,
            LOGGER_LEVEL: process.env.LOGGER_LEVEL,
            NODE_ENV: process.env.NODE_ENV,
            PAGE_SIZE: process.env.PAGE_SIZE,
            PORT: process.env.LISTEN_PORT,
            TOPOLOGY_FILE_PATH: process.env.TOPOLOGY_FILE_PATH
          },
          env_production: {
            NODE_ENV: 'production'
          },
          error_file: 'NULL',
          out_file: 'NULL'
        }
      ]
    }
