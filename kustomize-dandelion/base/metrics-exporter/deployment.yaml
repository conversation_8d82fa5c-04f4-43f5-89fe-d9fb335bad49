apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    io.kompose.service: dandelion-metrics-exporter
  name: dandelion-metrics-exporter
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: dandelion-metrics-exporter
  template:
    metadata:
      labels:
        io.kompose.service: dandelion-metrics-exporter
    spec:
      containers:
      - args:
        - --collector.disable-defaults
        - --collector.textfile
        - --collector.textfile.directory=/data
        image: prom/node-exporter
        imagePullPolicy: Always
        name: prom-node-exporter
        ports:
        - containerPort: 9100
          protocol: TCP
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /data
          name: node-exporter-data
      - name: socat-socket-server
        image: alpine/socat
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: "cardano-node-headless"
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        command: ["sh", "-c", "rm -f /ipc/node.socket; socat UNIX-LISTEN:/ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof"]
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
      - name: dandelion-metrics-exporter
        env:
        - name: CARDANO_NODE_SOCKET_PATH
          value: "/node-ipc/node.socket"
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST_RO
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: usernames
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: passwords
        - name: PGRST_DB_ANON_ROLE
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: usernames
        image: repsistance/dandelion-metrics-exporter:latest
        imagePullPolicy: Always
        resources: {}
        volumeMounts:
        - mountPath: /node-ipc
          name: node-ipc
        - mountPath: /data
          name: node-exporter-data
        - name: common-env
          mountPath: /run/secrets/common-env
          readOnly: true
        - name: postgres-password
          mountPath: /run/secrets/postgres-password
          readOnly: true
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - name: node-ipc
        emptyDir: {}
      - name: node-exporter-data
        emptyDir: {}
      - name: common-env
        configMap:
          name: common-env
      - name: postgres-password
        secret:
          secretName: init0-postgresql-ha-pgpool-custom-users
          items:
          - key: passwords
            path: POSTGREST_RO_PASSWORD
