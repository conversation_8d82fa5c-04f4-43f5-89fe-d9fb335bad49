apiVersion: batch/v1
kind: CronJob
metadata:
  name: koios-active-stake-cache-update
spec:
  schedule: "*/15 * * * *"
  jobTemplate:
    spec:
      backoffLimit: 0
      ttlSecondsAfterFinished: 540
      template:
        spec:
          serviceAccountName: koios-deploy-sql
          containers:
          - name: koios-active-stake-cache-update
            command: ["bash", "-x", "/configmap/active-stake-cache-update"]
            image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
            volumeMounts:
            - name: koios-configmap
              mountPath: /configmap
            - name: cardano-node-configmap
              mountPath: /cardano-node-configmap
            env:
            - name: KOIOS_REPOSITORY
              value: repsistance/koios-artifacts
            - name: KOIOS_BRANCH
              value: develop
            - name: CNODE_CONFIG_FILE
              value: /cardano-node-configmap/config.json
            - name: PGHOST
              valueFrom:
                configMapKeyRef:
                  name: common-env
                  key: POSTGRES_HOST_RW
            - name: PGPORT
              valueFrom:
                configMapKeyRef:
                  name: common-env
                  key: POSTGRES_PORT
            - name: PGDATABASE
              valueFrom:
                configMapKeyRef:
                  name: common-env
                  key: POSTGRES_DB
            - name: PGUSER
              valueFrom:
                configMapKeyRef:
                  name: common-env
                  key: POSTGRES_USER
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: init0-postgresql-ha-postgresql
                  key: password
            - name: POSTGRES_USER_RO
              valueFrom:
                secretKeyRef:
                  name: init0-postgresql-ha-pgpool-custom-users
                  key: usernames
            - name: POSTGRES_PASSWORD_RO
              valueFrom:
                secretKeyRef:
                  name: init0-postgresql-ha-pgpool-custom-users
                  key: passwords
            - name: PGRST_DB_SCHEMA
              value: grest
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          restartPolicy: Never
          volumes:
          - name: koios-configmap
            configMap:
              name: koios-configmap
          - name: cardano-node-configmap
            configMap:
              name: cardano-node
    
