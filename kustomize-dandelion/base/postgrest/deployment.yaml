apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    io.kompose.service: postgrest
  name: postgrest
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: postgrest
  template:
    metadata:
      labels:
        io.kompose.service: postgrest
    spec:
      initContainers:
      - name: configure
        command: ["bash", "-x", "/configmap/create-db-ro-user-entrypoint"]
        image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
        imagePullPolicy: IfNotPresent
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST_RW
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-postgresql
              key: password
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: usernames
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: passwords
        volumeMounts:
        - name: cardano-db-sync-configmap
          mountPath: /configmap
      containers:
      - name: postgrest
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST_RO
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: usernames
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: passwords
        - name: PGRST_DB_ANON_ROLE
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: usernames
        - name: PGRST_DB_SCHEMA
          value: public
        ports:
        - containerPort: 3000
        image: gimbalabs/postgrest:v9.0.0
        command: ["sh", "-c", "export PGRST_DB_URI=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}; /bin/postgrest"]
        imagePullPolicy: IfNotPresent
        resources: {}
        volumeMounts:
        - name: common-env
          mountPath: /run/secrets/common-env
          readOnly: true
        - name: postgres-password
          mountPath: /run/secrets/postgres-password
          readOnly: true
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 3
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - name: common-env
        configMap:
          name: common-env
      - name: postgres-password
        secret:
          secretName: init0-postgresql-ha-pgpool-custom-users
          items:
          - key: passwords
            path: POSTGREST_RO_PASSWORD
      - name: postgrest-configmap
        configMap:
          name: postgrest-configmap
      - name: cardano-db-sync-configmap
        configMap:
          name: cardano-db-sync-configmap
