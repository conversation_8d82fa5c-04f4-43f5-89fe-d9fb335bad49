apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    io.kompose.service: ogmios
  name: ogmios
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: ogmios
  template:
    metadata:
      labels:
        io.kompose.service: ogmios
    spec:
      initContainers:
      - name: setup-node-config
        command: ["bash", "/configmap/initContainer-entrypoint"]
        image: repsistance/cardano-node:iohk-mn-passive-8.1.2-0
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - name: ogmios-config
          mountPath: /config
        - name: ogmios-configmap
          mountPath: /configmap
      containers:
      - name: ogmios
        env:
        - name: NODE_CONFIG_PATH
          value: "/opt/cardano/cnode/files/config.json"
        - name: LISTEN_HOST
          value: "0.0.0.0"
        - name: LISTEN_PORT
          value: "1337"
        - name: NODE_SOCKET_PATH
          value: "/node-ipc/node.socket"
        - name: LOG_LEVEL
          value: "Debug"
        command: ["sh", "-xc", "ogmios --node-socket ${NODE_SOCKET_PATH} --host ${LISTEN_HOST} --port ${LISTEN_PORT} --log-level ${LOG_LEVEL} --node-config ${NODE_CONFIG_PATH}"]
        image: cardanosolutions/ogmios:v5.5.6-mainnet
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 1337
        resources: {}
        volumeMounts:
        - mountPath: /node-ipc
          name: node-ipc
        - name: ogmios-config
          mountPath: /opt/cardano/cnode/files
      - name: socat-socket-server
        image: alpine/socat
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: "cardano-node-headless"
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        command: ["sh", "-c", "rm -f /ipc/node.socket; socat UNIX-LISTEN:/ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof"]
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - name: node-ipc
        emptyDir: {}
      - name: ogmios-config
        emptyDir: {}
      - name: ogmios-configmap
        configMap:
          name: ogmios-configmap

