apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: kompose convert -c --controller deployment --volumes persistentVolumeClaim
    kompose.version: 1.21.0 (992df58d8)
  creationTimestamp: null
  labels:
    io.kompose.service: cardano-submit-api
  name: cardano-submit-api
spec:
  ports:
  - name: "8091"
    port: 8091
    targetPort: 8090
  selector:
    io.kompose.service: cardano-submit-api
status:
  loadBalancer: {}
