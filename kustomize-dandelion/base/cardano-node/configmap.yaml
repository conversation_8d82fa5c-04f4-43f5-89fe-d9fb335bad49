apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-node
data:
  initContainer-entrypoint: |
    if [[ ! -e /data/db/lock ]] && [[ "${RESTORE_SNAPSHOT}" == "true" ]]
    then
      curl -o - https://downloads.csnapshots.io/snapshots/${NETWORK}/$(curl -s https://downloads.csnapshots.io/snapshots/${NETWORK}/${NETWORK}-db-snapshot.json | jq -r .[].file_name ) | lz4 -c -d - | tar -x -C ${CNODE_DB_PATH}
    fi
  topology.json.passive: |
    {
       "Producers": [
          {
            "addr": "europe.relays-new.cardano-mainnet.iohk.io",
            "port": 3001,
            "valency": 2
          }
       ]
     }
  config.json: |
    {
      "ApplicationName": "cardano-sl",
      "ApplicationVersion": 1,
      "AlonzoGenesisFile": "/opt/cardano/cnode/files/alonzo-genesis.json",
      "AlonzoGenesisHash": "7e94a15f55d1e82d10f09203fa1d40f8eede58fd8066542cf6566008068ed874",
      "ByronGenesisFile": "/opt/cardano/cnode/files/byron-genesis.json",
      "ByronGenesisHash": "5f20df933584822601f9e3f8c024eb5eb252fe8cefb24d1317dc3d432e940ebb",
      "LastKnownBlockVersion-Alt": 0,
      "LastKnownBlockVersion-Major": 4,
      "LastKnownBlockVersion-Minor": 0,
      "MaxKnownMajorProtocolVersion": 4,
      "PBftSignatureThreshold": 0.9,
      "NetworkName": "mainnet",
      "MaxConcurrencyDeadline": 2,
      "MempoolCapacityBytesOverride": 256000000,
      "NumCoreNodes": 2,
      "Protocol": "Cardano",
      "RequiresNetworkMagic": "RequiresNoMagic",
      "ShelleyGenesisFile": "/opt/cardano/cnode/files/genesis.json",
      "ShelleyGenesisHash": "1a3be38bcbb7911969283716ad7aa550250226b76a61fc51cc9a9a35d9276d81",
      "ConwayGenesisFile": "/opt/cardano/cnode/files/conway-genesis.json",
      "ConwayGenesisHash": "f28f1c1280ea0d32f8cd3143e268650d6c1a8e221522ce4a7d20d62fc09783e1",
      "SocketPath": "/opt/cardano/cnode/sockets/node0.socket",
      "EnableLogMetrics": false,
      "EnableLogging": true,
      "TraceBlockFetchClient": true,
      "TraceBlockFetchDecisions": true,
      "TraceBlockFetchProtocol": true,
      "TraceBlockFetchProtocolSerialised": true,
      "TraceBlockFetchServer": true,
      "TraceChainDb": true,
      "TraceChainSyncBlockServer": true,
      "TraceChainSyncClient": true,
      "TraceChainSyncHeaderServer": true,
      "TraceChainSyncProtocol": true,
      "TraceDNSResolver": false,
      "TraceDNSSubscription": false,
      "TraceErrorPolicy": true,
      "TraceForge": true,
      "TraceHandshake": true,
      "TraceIpSubscription": true,
      "TraceLocalChainSyncProtocol": true,
      "TraceLocalErrorPolicy": true,
      "TraceLocalHandshake": false,
      "TraceLocalTxSubmissionProtocol": true,
      "TraceLocalTxSubmissionServer": true,
      "TraceMempool": true,
      "TraceMux": false,
      "TraceTxInbound": true,
      "TraceTxOutbound": true,
      "TraceTxSubmissionProtocol": true,
      "TracingVerbosity": "MaximalVerbosity",
      "TurnOnLogMetrics": false,
      "TurnOnLogging": true,
      "defaultBackends": [
        "KatipBK"
      ],
      "defaultScribes": [
        [
          "StdoutSK",
          "stdout"
        ]
      ],
      "hasEKG": 12788,
      "hasPrometheus": [
        "0.0.0.0",
        12798
      ],
      "minSeverity": "Info",
      "options": {
        "mapBackends": {
          "cardano.node.metrics.Forge": [
             "EKGViewBK"
          ],
          "cardano.node.metrics": [
            "EKGViewBK"
          ],
          "cardano.node.resources": [
            "EKGViewBK"
          ]
        },
        "mapSubtrace": {
          "cardano.node.metrics": {
            "subtrace": "Neutral"
          }
        }
      },
      "rotation": {
        "rpKeepFilesNum": 10,
        "rpLogLimitBytes": 10000000,
        "rpMaxAgeHours": 24
      },
      "setupBackends": [
        "KatipBK",
        "EKGViewBK"
      ],
      "setupScribes": [
        {
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }
