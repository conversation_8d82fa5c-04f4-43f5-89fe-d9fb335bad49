---
apiVersion: v1
kind: Service
metadata:
  labels:
    io.kompose.service: hasura
  name: hasura-headless
spec:
  sessionAffinity: ClientIP
  clusterIP: None
  type: ClusterIP
  ports:
  - name: hasura
    port: 8080
    targetPort: 8080
    protocol: TCP
  selector:
    io.kompose.service: hasura
status:
  loadBalancer: {}

---
#apiVersion: v1
#kind: Service
#metadata:
#  labels:
#    io.kompose.service: hasura
#  name: hasura
#spec:
#  ports:
#  - name: hasura
#    port: 8080
#    targetPort: 8080
#  selector:
#    io.kompose.service: hasura
#status:
#  loadBalancer: {}
#
