apiVersion: kustomize.config.k8s.io/v1alpha1  # <-- Component notation
kind: Component

patches:
# network affinity
- path: patches/base-affinity.yaml
  target:
    kind: StatefulSet
- path: patches/base-affinity.yaml
  target:
    kind: Deployment
- path: patches/base-affinity.yaml
  target:
    kind: DaemonSet
# anti-affinity (avoid cardano-node and graphql to share nodes with postgres/db-sync)
- path: patches/anti-affinity.yaml
  target:
    kind: StatefulSet
    name: cardano-node
- path: patches/anti-affinity.yaml
  target:
    kind: Deployment
    name: cardano-graphql
# affinity (per worker pool affinity)
- path: patches/cardano-graphql-affinity.yaml
  target:
    kind: Deployment
    name: cardano-graphql
- path: patches/cardano-node-affinity.yaml
  target:
    kind: StatefulSet
    name: cardano-node
- path: patches/db-main-affinity.yaml
  target:
    kind: StatefulSet
    name: init0-postgresql-ha-postgresql
- path: patches/db-main-affinity.yaml
  target:
    kind: Deployment
    name: init0-postgresql-ha-pgpool
- path: patches/db-main-affinity.yaml
  target:
    kind: StatefulSet
    name: cardano-db-sync
