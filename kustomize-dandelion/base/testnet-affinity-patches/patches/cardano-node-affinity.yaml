- op: add
  path: /spec/template/spec/tolerations
  value:
    - key: "k8s.dandelion.link/role"
      operator: "Equal"
      value: "single-node"
      effect: "PreferNoSchedule"
    - key: "k8s.dandelion.link/cardano-network"
      operator: "Equal"
      value: "testnet"
      effect: "PreferNoSchedule"

- op: add
  path: /spec/template/spec/affinity
  value:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: k8s.dandelion.link/role
            operator: In
            values:
            - single-node
          - key: k8s.dandelion.link/cardano-network
            operator: In
            values:
            - testnet
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        preference:
          matchExpressions:
            - key: k8s.dandelion.link/role
              operator: In
              values:
              - single-node
            - key: k8s.dandelion.link/cardano-network
              operator: In
              values:
              - testnet
