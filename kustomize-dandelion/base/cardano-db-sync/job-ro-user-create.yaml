apiVersion: batch/v1
kind: Job
metadata:
  name: create-db-ro-user
spec:
  backoffLimit: 5
  activeDeadlineSeconds: 600
  template:
    spec:
      containers:
      - name: create-db-ro-user
        command: ["bash", "-x", "/configmap/create-db-ro-user-entrypoint"]
        image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 0
        volumeMounts:
        - name: cardano-db-sync-configmap
          mountPath: /configmap
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST_RW
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-postgresql
              key: password
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: usernames
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: passwords
      restartPolicy: Never
      volumes:
      - name: cardano-db-sync-configmap
        configMap:
          name: cardano-db-sync-configmap
