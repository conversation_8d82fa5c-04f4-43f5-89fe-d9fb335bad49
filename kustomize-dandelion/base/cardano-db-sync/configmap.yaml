apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-db-sync-configmap
data:
  create-db-ro-user-entrypoint: |
    export PGCONNECT_TIMEOUT=10
    while [ -z "$(psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -c '\dt' ${POSTGRES_DB} | grep epoch)" ]
    do
      sleep 1
    done
    envsubst < /configmap/create-ro-user.sql.tpl > /tmp/create-ro-user.sql
    psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -f /tmp/create-ro-user.sql ${POSTGRES_DB}
    psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -f /configmap/extra.sql ${POSTGRES_DB}
  create-ro-user.sql.tpl: |
    CREATE ROLE ${POSTGRES_USER_RO};
    GRANT CONNECT ON DATABASE ${POSTGRES_DB} TO ${POSTGRES_USER_RO};
    GRANT SELECT ON ALL TABLES IN SCHEMA public TO ${POSTGRES_USER_RO};
    GRANT USAGE ON SCHEMA public TO ${POSTGRES_USER_RO};
    ALTER ROLE ${POSTGRES_USER_RO} WITH login;
    ALTER USER ${POSTGRES_USER_RO} WITH PASSWORD '${POSTGRES_PASSWORD_RO}';
    GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA PUBLIC TO ${POSTGRES_USER_RO};
  extra.sql: |
    CREATE OR REPLACE FUNCTION get_tx_history_for_addresses(data json) RETURNS TABLE (tx_hash text, block word31type, tx_timestamp timestamp) AS $$
    DECLARE
      addresses text[];
    BEGIN
      addresses := (SELECT array_agg(replace(rec::text, '"', ''))
                    FROM json_array_elements(data->'addresses') rec);
      RETURN QUERY (SELECT trim(txs.hash, '\\\\x') , txs.block_no, txs.time from (
        SELECT
          tx.id, tx.hash::text, block.block_no, block.hash::text as blockHash, block.time, tx.block_index
          FROM block
          INNER JOIN tx ON block.id = tx.block_id
          INNER JOIN tx_out ON tx.id = tx_out.tx_id
          WHERE tx_out.address = ANY(addresses)
        UNION
          SELECT DISTINCT
            tx.id, tx.hash::text, block.block_no, block.hash::text as blockHash, block.time, tx.block_index
            FROM block
            INNER JOIN tx ON block.id = tx.block_id
            INNER JOIN tx_in ON tx.id = tx_in.tx_in_id
            INNER JOIN tx_out ON (tx_in.tx_out_id = tx_out.tx_id) AND (tx_in.tx_out_index = tx_out.index)
            WHERE tx_out.address = ANY(addresses)
            ORDER BY time DESC
      ) AS txs);
    END; $$ LANGUAGE PLPGSQL IMMUTABLE;

    CREATE OR REPLACE FUNCTION get_eoe_balance_for_addresses(data json) RETURNS TABLE (balance numeric, address character varying) AS $$
    DECLARE
      addresses text[];
      epoch int;
    BEGIN
      addresses := (SELECT array_agg(replace(rec::text, '"', ''))
                    FROM json_array_elements(data->'addresses') rec);
      SELECT json_extract_path_text(data, 'epoch') INTO epoch AS tmp;
      RETURN QUERY (SELECT SUM(utxo_view.value), utxo_view.address FROM utxo_view
        INNER JOIN tx ON tx.id = utxo_view.tx_id
        INNER JOIN block ON block.id = tx.block_id
        WHERE utxo_view.address = ANY(addresses)
        AND block.slot_no <= (select get_last_slot_for_epoch(epoch))
        GROUP BY utxo_view.address);
    END; $$ LANGUAGE PLPGSQL IMMUTABLE;

    CREATE FUNCTION get_metadata(metadatum word64type default 0, epochs int[] default null) RETURNS TABLE (epoch word31type, data jsonb) AS $$
    BEGIN
      IF epochs IS NOT NULL THEN
        RETURN QUERY (select block.epoch_no, json as epoch from tx_metadata
                      inner join tx on tx_metadata.tx_id = tx.id
                      inner join block on tx.block_id = block.id
                      where key = metadatum and epoch_no = ANY(epochs)
                      order by epoch_no);
      ELSE
        RETURN QUERY (select block.epoch_no, json as epoch from tx_metadata
                      inner join tx on tx_metadata.tx_id = tx.id
                      inner join block on tx.block_id = block.id
                      where key = metadatum
                      order by epoch_no);
      END IF;
    END; $$ LANGUAGE PLPGSQL IMMUTABLE;

    CREATE OR REPLACE FUNCTION get_pools_dbsync_hash_id(_pool_bech32_ids text[] DEFAULT null) RETURNS TABLE (pool_dbsync_hash_id bigint, pool_bech32_id character varying, vrf_key_hash character varying) AS $$
    select distinct pool_hash.id, pool_hash.view, encode(pool_update.vrf_key_hash, 'hex') from pool_update 
                  inner join pool_hash on pool_update.hash_id = pool_hash.id
                  where pool_update.registered_tx_id in (select max(pool_update.registered_tx_id) from pool_update group by hash_id)
                  and not exists
                  ( select * from pool_retire where pool_retire.hash_id = pool_update.hash_id
                    and pool_retire.retiring_epoch <= (select max (epoch_no) from block)
                  ) 
                  AND CASE
                  WHEN _pool_bech32_ids IS NULL THEN true
                  WHEN _pool_bech32_ids IS NOT NULL THEN pool_hash.view = ANY(SELECT UNNEST(_pool_bech32_ids))
                  END;
    $$ LANGUAGE SQL IMMUTABLE;
  initContainer-restore-snapshot-entrypoint: |
    function finish-socat {
      pkill -f socat && sleep 1
      rm -f ${CARDANO_NODE_SOCKET_PATH}
    }

    trap finish-socat EXIT

    export PGCONNECT_TIMEOUT=10

    case "${NETWORK}" in
      "mainnet") S3_BUCKET_URI=${DB_SYNC_SNAPSHOT_MAINNET_S3_BUCKET_URI};;
      "testnet") S3_BUCKET_URI=${DB_SYNC_SNAPSHOT_TESTNET_S3_BUCKET_URI};;
    esac

    if [ "${RESTORE_SNAPSHOT}" == "true" ]
    then
      until psql -P pager=off -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -c '\dt' ${POSTGRES_DB}
      do
        sleep 1
      done
      if [ -z "$(psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -c '\dt' ${POSTGRES_DB} | grep epoch)" ]
      then
        cd /aux-data-dir

        read -d "\n" DB_SYNC_SNAPSHOT DB_SYNC_SNAPSHOT_SHA256_SUM <<<$(aws s3 ls --no-sign-request ${S3_BUCKET_URI} | awk '{print $NF}' | sort | tail -n2)

        aws s3 cp --no-sign-request ${S3_BUCKET_URI}${DB_SYNC_SNAPSHOT} snapshot.tgz
        aws s3 cp --no-sign-request ${S3_BUCKET_URI}${DB_SYNC_SNAPSHOT_SHA256_SUM} snapshot.tgz.sha256sum.tmp

        echo $(awk '{print $1}' snapshot.tgz.sha256sum.tmp) snapshot.tgz > snapshot.tgz.sha256sum
        sha256sum --check --status < snapshot.tgz.sha256sum
        if [ $? -eq 0 ]
        then
          tar -zxf snapshot.tgz && rm -f snapshot.tgz
          psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -f *sql ${POSTGRES_DB}
          rm -f *sql
          mv *state* /db-sync-statedir
          chown -R root: /db-sync-statedir
        else
          echo "[!] sha256sum check error for ${S3_BUCKET_URI}${DB_SYNC_SNAPSHOT}"
          exit 1
        fi
      fi
    fi
    DB_BLOCK_NO=$(psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -tA -P pager=off -c 'select block_no from block order by id desc limit 1;' ${POSTGRES_DB})
    test -z "${DB_BLOCK_NO}" && DB_BLOCK_NO=0

    echo -n "[+] Waiting for cardano-node to become available (socat approach makes db-sync to freeze if it's not)..."
    while ! `echo > /dev/tcp/${CARDANO_NODE_SOCKET_TCP_HOST}/${CARDANO_NODE_SOCKET_TCP_PORT}`
    do
      sleep 1
      echo -n .
    done

    rm -f ${CARDANO_NODE_SOCKET_PATH}
    socat UNIX-LISTEN:${CARDANO_NODE_SOCKET_PATH},fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof &
    while ! `timeout 10 cardano-cli query tip --mainnet 2>&1 | grep -q "epoch\|NodeToClientVersionData"`
    do
      sleep 1
      echo -n .
    done

    cardano-cli query tip --mainnet
    if [ $? -eq 0 ]
    then
      MAGIC_ARG="--mainnet"
    else
      MAGIC_ARG="--testnet-magic $(cardano-cli query tip --mainnet 2>&1 | sed 's|\(.*NodeToClientVersionData.*\)unNetworkMagic = \(.*\)}}.*/=\(.*\)|\2|g' | xargs echo)"
    fi

    echo -n "[+] Waiting for cardano-node to reach tip..."
    while [[ -n $(cardano-cli query tip ${MAGIC_ARG} | jq -r .block) ]] && [ ${DB_BLOCK_NO} -gt $(cardano-cli query tip ${MAGIC_ARG} | jq -r .block) ]
    do
      sleep 60
      echo -n .
    done

  initContainer-setup-node-config-entrypoint: |
    rm -rf /config/lost+found

    cp /opt/cardano/cnode/files/config.json /config/cardano-node-config.json
    cp /opt/cardano/cnode/files/byron-genesis.json /config/
    cp /opt/cardano/cnode/files/genesis.json /config/
    cp /opt/cardano/cnode/files/alonzo-genesis.json /config/

  entrypoint: |
    set +x
    export PGPASSFILE=/tmp/pgpassfile
    echo "${POSTGRES_HOST}:${POSTGRES_PORT}:${POSTGRES_DB}:${POSTGRES_USER}:${POSTGRES_PASSWORD}" > ${PGPASSFILE}
    chmod 0600 ${PGPASSFILE}
    set -x

    CARDANO_DB_SYNC_BINARY=$(find /nix/store/*cardano-db-sync-exe-cardano-db-sync*/bin -name cardano-db-sync)
    CARDANO_DB_SYNC_SCHEMA_DIR=$(find /nix/store/*cardano-db-sync-schema*/ -name schema -type d)
    if [ "${DISABLE_EPOCH}" == "true" ]; then DISABLE_EPOCH="--disable-epoch"; else DISABLE_EPOCH=""; fi
    if [ "${DISABLE_CACHE}" == "true" ]; then DISABLE_CACHE="--disable-cache"; else DISABLE_CACHE=""; fi
    if [ "${DISABLE_LEDGER}" == "true" ]; then DISABLE_LEDGER="--disable-ledger"; else DISABLE_LEDGER=""; fi
    exec ${CARDANO_DB_SYNC_BINARY} \
      ${DISABLE_EPOCH} \
      ${DISABLE_CACHE} \
      ${DISABLE_LEDGER} \
      --config /configmap/cardano-db-sync-config.json \
      --socket-path "$CARDANO_NODE_SOCKET_PATH" \
      --schema-dir "${CARDANO_DB_SYNC_SCHEMA_DIR}" \
      --state-dir "${CARDANO_DB_SYNC_STATE_DIR}"

  liveness-healthcheck: |
    KUBE_API_URL=https://kubernetes.default.svc/api/v1/namespaces
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)
    SINCE_SECONDS=300
    curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods/cardano-db-sync-0/log?container=cardano-db-sync&sinceSeconds=${SINCE_SECONDS}" > /tmp/db-sync-log-tail 
    if [ $? -ne 0 ]
    then
      exit 0
    else
      grep -q "libpq.*failed.*no connection to the server" /tmp/db-sync-log-tail
      if [ $? -eq 0 ]
      then
        echo "[!] Postgres connection lost, failing healthcheck..."
        exit 1
      fi
    fi

  cardano-db-sync-config.json: |
    {
      "EnableLogMetrics": false,
      "EnableLogging": true,
      "NetworkName": "mainnet",
      "NodeConfigFile": "/opt/cardano/cnode/files/cardano-node-config.json",
      "PrometheusPort": 8080,
      "RequiresNetworkMagic": "RequiresNoMagic",
      "defaultBackends": [
        "KatipBK"
      ],
      "defaultScribes": [
        [
          "StdoutSK",
          "stdout"
        ]
      ],
      "minSeverity": "Info",
      "options": {
        "cfokey": {
          "value": "Release-1.0.0"
        },
        "mapBackends": {},
        "mapSeverity": {
          "db-sync-node": "Info",
          "db-sync-node.Mux": "Error",
          "db-sync-node.Subscription": "Error"
        },
        "mapSubtrace": {
          "#ekgview": {
            "contents": [
              [
                {
                  "contents": "cardano.epoch-validation.benchmark",
                  "tag": "Contains"
                },
                [
                  {
                    "contents": ".monoclock.basic.",
                    "tag": "Contains"
                  }
                ]
              ],
              [
                {
                  "contents": "cardano.epoch-validation.benchmark",
                  "tag": "Contains"
                },
                [
                  {
                    "contents": "diff.RTS.cpuNs.timed.",
                    "tag": "Contains"
                  }
                ]
              ],
              [
                {
                  "contents": "#ekgview.#aggregation.cardano.epoch-validation.benchmark",
                  "tag": "StartsWith"
                },
                [
                  {
                    "contents": "diff.RTS.gcNum.timed.",
                    "tag": "Contains"
                  }
                ]
              ]
            ],
            "subtrace": "FilterTrace"
          },
          "#messagecounters.aggregation": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.ekgview": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.katip": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.monitoring": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.switchboard": {
            "subtrace": "NoTrace"
          },
          "benchmark": {
            "contents": [
              "GhcRtsStats",
              "MonotonicClock"
            ],
            "subtrace": "ObservableTrace"
          },
          "cardano.epoch-validation.utxo-stats": {
            "subtrace": "NoTrace"
          }
        }
      },
      "rotation": {
        "rpKeepFilesNum": 10,
        "rpLogLimitBytes": 5000000,
        "rpMaxAgeHours": 24
      },
      "setupBackends": [
        "AggregationBK",
        "KatipBK"
      ],
      "setupScribes": [
        {
          "scFormat": "ScText",
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }
