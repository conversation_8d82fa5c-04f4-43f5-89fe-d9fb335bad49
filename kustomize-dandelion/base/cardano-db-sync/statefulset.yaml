apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    io.kompose.service: cardano-db-sync
  name: cardano-db-sync
spec:
  replicas: 1
  updateStrategy:
    type: OnDelete
  selector:
    matchLabels:
      io.kompose.service: cardano-db-sync
  serviceName: cardano-db-sync
  template:
    metadata:
      labels:
        io.kompose.service: cardano-db-sync
    spec:
      initContainers:
      - name: setup-node-config
        command: ["bash", "/configmap/initContainer-setup-node-config-entrypoint"]
        image: repsistance/cardano-node:iohk-mn-passive-8.1.2-0
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - name: cardano-node-config
          mountPath: /config
        - name: cardano-db-sync-configmap
          mountPath: /configmap
      - name: restore-snapshot
        command: ["bash", "-x", "/configmap/initContainer-restore-snapshot-entrypoint"]
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 0
        image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
        imagePullPolicy: IfNotPresent
        env:
        - name: NETWORK
          value: mainnet
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: "cardano-node-headless"
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        - name: SOCAT_TIMEOUT
          value: "3600"
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        - name: RESTORE_SNAPSHOT
          value: "true"
        - name: DB_SYNC_SNAPSHOT_TESTNET_S3_BUCKET_URI
          value: "s3://updates-cardano-testnet/cardano-db-sync/13.1/"
        - name: DB_SYNC_SNAPSHOT_MAINNET_S3_BUCKET_URI
          value: "s3://update-cardano-mainnet.iohk.io/cardano-db-sync/13.1/"
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST_RW
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-postgresql
              key: password
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: usernames
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: passwords
        volumeMounts:
        - name: cardano-db-sync-configmap
          mountPath: /configmap
        - name: dbsync-statedir
          mountPath: /db-sync-statedir
        - name: node-ipc
          mountPath: /aux-data-dir
        - name: node-ipc
          mountPath: /ipc
      containers:
      - name: cardano-db-sync
        env:
        - name: EXTENDED
          value: "true"
        - name: NETWORK
          value: mainnet
        - name: DISABLE_EPOCH
          value: "false"
        - name: DISABLE_CACHE
          value: "false"
        - name: DISABLE_LEDGER
          value: "false"
        - name: CARDANO_DB_SYNC_STATE_DIR
          value: /var/lib/cexplorer
        - name: CARDANO_NODE_SOCKET_PATH
          value: "/node-ipc/node.socket"
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-postgresql
              key: password
        image: gimbalabs/cardano-db-sync:13.0.5
        workingDir: /var/lib/cexplorer
        command: ["sh", "-x", "/configmap/entrypoint"]
        #command: ["sh", "-c", "while true; do sleep 6969; done"]
        imagePullPolicy: IfNotPresent
        resources: {}
        volumeMounts:
        - name: node-ipc
          mountPath: /node-ipc
        # workaround for missing tmp dir :facepalm:
        - name: node-ipc
          mountPath: /tmp
        - name: common-env
          mountPath: /run/secrets/common-env
          readOnly: true
        - name: postgres-password
          mountPath: /run/secrets/postgres-password
          readOnly: true
        - name: dbsync-statedir
          mountPath: /var/lib/cexplorer
        - name: cardano-db-sync-configmap
          mountPath: /configmap
        - name: cardano-node-config
          mountPath: /opt/cardano/cnode/files
        livenessProbe:
          exec:
            command: ["bash", "/configmap/liveness-healthcheck"]
          initialDelaySeconds: 600
          periodSeconds: 60
          timeoutSeconds: 120
      - name: socat-socket-server
        image: alpine/socat
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: "cardano-node-headless"
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        - name: SOCAT_TIMEOUT
          value: "3600"
        command: ["sh", "-c", "rm -f /ipc/node.socket; socat UNIX-LISTEN:/ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof"]
        #command: ["sh", "-c", "socat -d -d -d -d -v UNIX-LISTEN:/ipc/node.socket TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof,keepalive,keepidle=10,keepintvl=10,keepcnt=100"]
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - name: node-ipc
        emptyDir: {}
      - name: cardano-node-config
        emptyDir: {}
      - name: common-env
        configMap:
          name: common-env
      - name: postgres-password
        secret:
          secretName: init0-postgresql-ha-postgresql
          items:
          - key: password
            path: POSTGRES_PASSWORD
      - name: dbsync-statedir
        persistentVolumeClaim:
          claimName: dbsync-statedir
      - name: cardano-db-sync-configmap
        configMap:
          name: cardano-db-sync-configmap

  volumeClaimTemplates:
  - metadata:
      name: dbsync-statedir
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 4Gi
