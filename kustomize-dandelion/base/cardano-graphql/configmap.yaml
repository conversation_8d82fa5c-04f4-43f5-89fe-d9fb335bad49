apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-graphql-configmap
data:
  liveness-healthcheck: |
    HEALTHCHECK_TIMEOUT=5
    timeout ${HEALTHCHECK_TIMEOUT} curl -w %{http_code} -k http://localhost:3100 -H 'Accept-Encoding: gzip, deflate, br' -H 'Content-Type: application/json' -H 'Accept: application/json' -H 'Connection: keep-alive' -H 'DNT: 1' --data-binary '{"query":"fragment _Sync on Query {\n  cardanoDbMeta {\n    initialized\n    syncPercentage\n    __typename\n  }\n  __typename\n}\n\nfragment _GetTip on Query {\n  cardano {\n    tip {\n      number\n      slotNo\n      slotInEpoch\n      epochNo\n      forgedAt\n      __typename\n    }\n    __typename\n  }\n  __typename\n}\n\nquery PaymentAddresses {\n  ..._Sync\n  ..._GetTip\n}\n"}' | grep -iq initialized.*true
    if [ $? -ne 0 ]
    then
      exit 1
    fi
  initContainer-entrypoint: |
    rm -rf /config/lost+found

    CNODE_FILES="config.json byron-genesis.json genesis.json alonzo-genesis.json"

    for file in ${CNODE_FILES}
    do
      cp -a /opt/cardano/cnode/files/${file} /config
    done

  initContainer-wait-for-tip: |

    function finish-socat {
      pkill -f socat && sleep 1
      rm -f ${CARDANO_NODE_SOCKET_PATH}
    }

    trap finish-socat EXIT

    export PGCONNECT_TIMEOUT=10

    echo -n "[+] Waiting for cardano-node to become available (socat approach makes db-sync to freeze if it's not)..."
    while ! `echo > /dev/tcp/${CARDANO_NODE_SOCKET_TCP_HOST}/${CARDANO_NODE_SOCKET_TCP_PORT}`
    do
      sleep 1
      echo -n .
    done

    rm -f ${CARDANO_NODE_SOCKET_PATH}
    socat UNIX-LISTEN:${CARDANO_NODE_SOCKET_PATH},fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof &
    while ! `timeout 10 cardano-cli query tip --mainnet 2>&1 | grep -q "epoch\|NodeToClientVersionData"`
    do
      sleep 1
      echo -n .
    done

    cardano-cli query tip --mainnet
    if [ $? -eq 0 ]
    then
      MAGIC_ARG="--mainnet"
    else
      MAGIC_ARG="--testnet-magic $(cardano-cli query tip --mainnet 2>&1 | sed 's|\(.*NodeToClientVersionData.*\)unNetworkMagic = \(.*\)}}.*/=\(.*\)|\2|g' | xargs echo)"
    fi

    DB_BLOCK_NO=$(psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -tA -P pager=off -c 'select block_no from block order by id desc limit 1;' ${POSTGRES_DB})
    test -z "${DB_BLOCK_NO}" && DB_BLOCK_NO=0
    echo -n "[+] Waiting for cardano-node to reach tip..."
    while [[ -n $(cardano-cli query tip ${MAGIC_ARG} | jq -r .block) ]] && [ ${DB_BLOCK_NO} -gt $(cardano-cli query tip ${MAGIC_ARG} | jq -r .block) ]
    do
      sleep 60
      echo -n .
    done

    export KUBE_API_URL="https://kubernetes.default.svc/api/v1/namespaces"
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)

    echo -n "[!] Waiting for db-sync to be initialized..."
    while [[ -z $(curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods" | jq -r '.items[] | select(.status.phase=="Running") | .metadata.name' | grep ^cardano-db-sync) ]]
    do
      echo -n .
      sleep 20
    done

