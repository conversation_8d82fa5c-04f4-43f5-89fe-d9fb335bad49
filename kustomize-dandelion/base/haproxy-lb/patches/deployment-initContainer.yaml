- op: add
  path: /spec/template/spec/initContainers
  value:
      - name: configure
        command: ["bash", "-x", "/configmap/initContainer-entrypoint"]
        image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
        imagePullPolicy: IfNotPresent
        env:
        - name: TOP_DOMAIN
          value: "dandelion.link"
        - name: SUB_DOMAIN
          value: "d"
        - name: HAPROXY_MAXCONN_PER_BACKEND
          value: "10"
        - name: CARDANO_MAX_TIP_DRIFT
          value: "600"
        - name: NETWORKS
          value: "mainnet iohk-preprod iohk-preview"
        - name: APIS
          value: "graphql-api submit-api explorer-api ogmios-api postgrest-api rosetta-api koios-api cardano-token-registry-api blockfrost-api kupo-api"
        - name: HEALTHCHECKS_TEMPLATE_DIR
          value: "/configmap"
        - name: TOPOLOGY_DIR
          value: "/configmap"
        - name: CONFIG_OUTPUT_DIR
          value: "/output"
        volumeMounts:
        - name: dandelion-haproxy-configmap
          mountPath: /configmap
        - name: extra-conf
          mountPath: /output


- op: add
  path: /spec/template/spec/volumes/-
  value:
    name: dandelion-haproxy-configmap
    configMap:
      name: dandelion-haproxy-configmap
      defaultMode: 0755

- op: add
  path: /spec/template/spec/containers/0/volumeMounts/-
  value:
    name: extra-conf
    mountPath: /extra-conf

- op: add
  path: /spec/template/spec/containers/0/volumeMounts/-
  value:
    name: dandelion-haproxy-configmap
    mountPath: /configmap
