apiVersion: kustomize.config.k8s.io/v1alpha1  # <-- Component notation
kind: Component

patches:
# cardano-node
- path: patches/cardano-node-pvc.yaml
  target:
    kind: StatefulSet
    name: cardano-node
# cardano-graphql
- path: patches/genesis-image.yaml
  target:
    kind: Deployment
    name: cardano-graphql
# cardano-db-sync
- path: patches/cardano-db-sync-deployment-image.yaml
  target:
    kind: StatefulSet
    name: cardano-db-sync
- path: patches/cardano-db-sync-deployment-pvc.yaml
  target:
    kind: StatefulSet
    name: cardano-db-sync
# cardano-rosetta
- path: patches/cardano-rosetta-deployment-image.yaml
  target:
    kind: Deployment
    name: cardano-rosetta
# postgres-ha
- path: patches/postgres-ha-pvc.yaml
  target:
    kind: StatefulSet
    name: init0-postgresql-ha-postgresql
- path: patches/postgres-dev-shm-mount.yaml
  target:
    kind: StatefulSet
    name: init0-postgresql-ha-postgresql
- path: patches/postgres-extended-configuration-configmap.yaml
  target:
    kind: ConfigMap
    name: init0-postgresql-ha-postgresql-extended-configuration
