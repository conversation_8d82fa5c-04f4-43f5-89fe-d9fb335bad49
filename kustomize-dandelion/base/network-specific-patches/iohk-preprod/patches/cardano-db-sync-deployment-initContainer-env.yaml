- op: replace
  path: /spec/template/spec/initContainers/1/env
  value:
    - name: RESTORE_SNAPSHOT
      value: "false"
    - name: NETWORK
      value: "preprod"
    - name: CARD<PERSON><PERSON>_NODE_SOCKET_TCP_HOST
      value: "cardano-node-headless"
    - name: CA<PERSON><PERSON><PERSON>_NODE_SOCKET_TCP_PORT
      value: "30000"
    - name: SOCAT_TIMEOUT
      value: "3600"
    - name: CARD<PERSON>O_NODE_SOCKET_PATH
      value: /ipc/node.socket
    - name: DB_SYNC_SNAPSHOT_TESTNET_S3_BUCKET_URI
      value: "s3://updates-cardano-testnet/cardano-db-sync/13/"
    - name: <PERSON>_SY<PERSON>_SNAPSHOT_MAINNET_S3_BUCKET_URI
      value: "s3://update-cardano-mainnet.iohk.io/cardano-db-sync/12/"
    - name: POSTGRES_HOST
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_HOST_RW
    - name: POSTGRES_PORT
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_PORT
    - name: POSTGRES_DB
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_DB
    - name: POSTGRES_USER
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_USER
    - name: PGPASSWORD
      valueFrom:
        secretKeyRef:
          name: init0-postgresql-ha-postgresql
          key: password
    - name: POSTGRES_USER_RO
      valueFrom:
        secretKeyRef:
          name: init0-postgresql-ha-pgpool-custom-users
          key: usernames
    - name: POSTGRES_PASSWORD_RO
      valueFrom:
        secretKeyRef:
          name: init0-postgresql-ha-pgpool-custom-users
          key: passwords

