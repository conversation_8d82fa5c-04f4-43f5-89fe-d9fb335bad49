- op: replace
  path: /spec/template/spec/containers/0/env
  value:
    - name: LOGGER_MIN_SEVERITY
      value: "info"
    - name: HASURA_URI
      value: http://hasura-headless:8080
    - name: METADATA_SERVER_URI
      value: https://d.cardano-token-registry-api.mainnet.dandelion.link
    - name: OGMIOS_HOST
      value: ogmios
    - name: OGMIOS_PORT
      value: "1337"
    - name: POSTGRES_HOST
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_HOST_RW
    - name: POSTGRES_PORT
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_PORT
    - name: POSTGRES_DB_FILE
      value: /run/secrets/common-env/POSTGRES_DB
    - name: POSTGRES_USER_FILE
      value: /run/secrets/common-env/POSTGRES_USER
    - name: POSTGRES_PASSWORD_FILE
      value: /run/secrets/postgres-password/POSTGRES_PASSWORD
    - name: <PERSON><PERSON><PERSON><PERSON>_NODE_SOCKET_PATH
      value: /ipc/node.socket
