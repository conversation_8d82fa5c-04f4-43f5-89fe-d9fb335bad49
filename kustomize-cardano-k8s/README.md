# Cardano Kubernetes Deployment

This directory contains simplified Kubernetes manifests for deploying Cardano Node and DB Sync services using Kustomize.

## Educational Purpose

These manifests are designed for educational purposes with:
- Simplified configurations with sensible defaults
- Clear documentation and comments
- Minimal blockchain knowledge requirements
- Production-ready but beginner-friendly approach

## Structure

- `base/` - Base Kubernetes resources
  - `cardano-node-simple/` - Cardano Node deployment
  - `cardano-db-sync-simple/` - Cardano DB Sync deployment  
  - `postgres-simple/` - PostgreSQL database
  - `common-simple/` - Shared configurations

- `overlays/` - Environment-specific configurations
  - `simple-testnet/` - Testnet deployment
  - `simple-mainnet/` - Mainnet deployment

## Quick Start

Deploy to testnet:
```bash
kubectl apply -k overlays/simple-testnet/
```

Deploy to mainnet:
```bash
kubectl apply -k overlays/simple-mainnet/
```

## Requirements

- Kubernetes cluster with persistent volume support
- At least 16GB storage for testnet, 100GB+ for mainnet
- 4GB+ RAM recommended

## Services Overview

### Cardano Node
- **Purpose**: Core Cardano blockchain node
- **Image**: `ghcr.io/intersectmbo/cardano-node:10.1.4`
- **Ports**: 3001 (P2P), 12788 (EKG metrics)
- **Storage**: Blockchain data (~8GB testnet, ~100GB+ mainnet)

### Cardano DB Sync
- **Purpose**: Synchronizes blockchain data to PostgreSQL
- **Image**: `ghcr.io/intersectmbo/cardano-db-sync:13.6.0.5`
- **Dependencies**: Cardano Node, PostgreSQL
- **Storage**: Application state data

### PostgreSQL
- **Purpose**: Database for blockchain data
- **Image**: `postgres:17.2-alpine`
- **Storage**: Database files
- **Credentials**: Configured via Kubernetes secrets

## Configuration

The deployment uses environment variables to configure the network:
- `NETWORK=testnet` for testnet deployment
- `NETWORK=mainnet` for mainnet deployment

## Monitoring

- Cardano Node exposes EKG metrics on port 12788
- Health checks are configured for all services
- Logs are available via `kubectl logs`

## Persistence

All services use persistent volumes to maintain data across pod restarts:
- Cardano Node: Blockchain database
- DB Sync: Application state
- PostgreSQL: Database files
- Shared IPC: Unix socket communication between services
