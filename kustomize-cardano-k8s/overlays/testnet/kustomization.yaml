apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: cardano-testnet

resources:
- namespace.yaml
- ../../base/common
- ../../base/postgres
- ../../base/cardano-node
- ../../base/cardano-db-sync

patches:
- path: network-config-patch.yaml
- path: storage-patch.yaml
- path: mithril-patch.yaml

configMapGenerator:
- name: common-env
  behavior: merge
  literals:
  - NETWORK=preprod
  - MITHRIL_AGGREGATOR_ENDPOINT=https://aggregator.release-preprod.api.mithril.network/aggregator
  - MITHRIL_GENESIS_VERIFICATION_KEY=5b3132372c37332c3132342c3136312c362c3133372c3133312c3231332c3230372c3131372c3139382c38352c3137362c3139392c3136322c3234312c36382c3132332c3131392c3134352c31332c3233322c3234332c34392c3232392c322c3234392c3230352c3230352c33392c3233352c34345d

namespace: cardano-testnet
