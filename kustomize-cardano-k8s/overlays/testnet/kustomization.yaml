apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Metadata for this overlay
metadata:
  annotations:
    config.kubernetes.io/local-config: "true"
  name: cardano-testnet

# Common labels for all resources in this overlay

# Common annotations
commonAnnotations:
  description: Cardano testnet deployment with socat TCP transport
  maintainer: cardano-k8s

# Base resources to include
resources:
- namespace.yaml
- ../../base/common
- ../../base/postgres
- ../../base/cardano-node
- ../../base/cardano-db-sync

# Patches to customize for testnet

# ConfigMap generator to override network settings
configMapGenerator:
- behavior: merge
  literals:
  - NETWORK=preprod
  name: common-env

# Namespace for this deployment
namespace: cardano-testnet
labels:
- includeSelectors: true
  pairs:
    cardano.network: testnet
    deployment.type: standard
    environment: testnet
patches:
- path: storage-patch.yaml
