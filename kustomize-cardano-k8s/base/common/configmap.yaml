apiVersion: v1
kind: ConfigMap
metadata:
  name: common-env
data:
  # PostgreSQL connection settings
  POSTGRES_HOST: "postgres"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "cexplorer"
  POSTGRES_USER: "postgres"
  
  # Cardano Node settings
  CARDANO_NODE_SOCKET_PATH: "/ipc/node.socket"

  # Socat TCP transport settings
  CARDANO_NODE_SOCKET_TCP_HOST: "cardano-node"
  CARDANO_NODE_SOCKET_TCP_PORT: "30000"
  SOCAT_TIMEOUT: "3600"

  # Network configuration (will be overridden in overlays)
  NETWORK: ""

  # Mithril configuration for fast bootstrapping (mainnet by default)
  MITHRIL_AGGREGATOR_ENDPOINT: "https://aggregator.release-mainnet.api.mithril.network/aggregator"
  MITHRIL_GENESIS_VERIFICATION_KEY: "5b3139312c36362c3134302c3138352c3133382c31312c3233372c3230372c3235302c3134342c32372c322c3138382c33302c31322c38312c3135352c3230342c31302c3137392c37352c32332c3133382c3139362c3231372c352c31342c32302c35372c37392c33392c3137365d"
  MITHRIL_SNAPSHOT_DIGEST: "latest"
  ENABLE_MITHRIL_BOOTSTRAP: "true"
