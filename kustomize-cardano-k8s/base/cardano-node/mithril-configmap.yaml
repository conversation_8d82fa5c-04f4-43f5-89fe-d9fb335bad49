apiVersion: v1
kind: ConfigMap
metadata:
  name: mithril-config
data:
  mithril-bootstrap.sh: |
    #!/bin/bash
    set -euo pipefail
    
    echo "Starting Mithril bootstrap process..."
    
    # Check if Mithril bootstrap is enabled
    if [ "${ENABLE_MITHRIL_BOOTSTRAP:-false}" != "true" ]; then
      echo "Mithril bootstrap disabled, skipping..."
      exit 0
    fi
    
    # Check if database already exists and has data
    if [ -d "/data/db/immutable" ] && [ "$(ls -A /data/db/immutable 2>/dev/null)" ]; then
      echo "Database already exists with data, skipping Mithril bootstrap..."
      exit 0
    fi
    
    echo "Database is empty, proceeding with Mithril bootstrap..."
    
    # Create necessary directories
    mkdir -p /data/db
    mkdir -p /tmp/mithril
    
    # Set Mithril environment variables
    export MITHRIL_AGGREGATOR_ENDPOINT="${MITHRIL_AGGREGATOR_ENDPOINT}"
    export MITHRIL_GENESIS_VERIFICATION_KEY="${MITHRIL_GENESIS_VERIFICATION_KEY}"
    
    # Download and extract the latest snapshot
    echo "Downloading Mithril snapshot..."
    cd /tmp/mithril
    
    # Get the latest snapshot information
    if [ "${MITHRIL_SNAPSHOT_DIGEST}" = "latest" ]; then
      SNAPSHOT_DIGEST=$(mithril-client snapshot list --json | jq -r '.[0].digest')
    else
      SNAPSHOT_DIGEST="${MITHRIL_SNAPSHOT_DIGEST}"
    fi
    
    echo "Using snapshot digest: ${SNAPSHOT_DIGEST}"
    
    # Download the snapshot
    mithril-client snapshot download ${SNAPSHOT_DIGEST} --download-dir /tmp/mithril
    
    # Extract the snapshot to the database directory
    echo "Extracting snapshot to database directory..."
    SNAPSHOT_ARCHIVE=$(find /tmp/mithril -name "*.tar.gz" | head -1)
    
    if [ -n "${SNAPSHOT_ARCHIVE}" ]; then
      echo "Extracting ${SNAPSHOT_ARCHIVE}..."
      tar -xzf "${SNAPSHOT_ARCHIVE}" -C /data/db --strip-components=1
      echo "Mithril bootstrap completed successfully!"
    else
      echo "Error: No snapshot archive found!"
      exit 1
    fi
    
    # Clean up temporary files
    rm -rf /tmp/mithril
    
    echo "Mithril bootstrap process completed."

  mithril-client-config.json: |
    {
      "aggregator_endpoint": "${MITHRIL_AGGREGATOR_ENDPOINT}",
      "genesis_verification_key": "${MITHRIL_GENESIS_VERIFICATION_KEY}",
      "network": "${NETWORK}",
      "cardano_node_version": "10.1.4"
    }
