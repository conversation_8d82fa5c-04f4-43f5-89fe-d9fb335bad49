apiVersion: v1
kind: ConfigMap
metadata:
  name: mithril-config
data:
  mithril-bootstrap.sh: |
    #!/bin/bash
    set -euo pipefail

    echo "Starting Mithril bootstrap..."

    # Skip if bootstrap disabled
    if [ "${ENABLE_MITHRIL_BOOTSTRAP:-false}" != "true" ]; then
      echo "Mithril bootstrap disabled, skipping..."
      exit 0
    fi

    # Skip if database already has data
    if [ -d "/data/db/immutable" ] && [ "$(ls -A /data/db/immutable 2>/dev/null)" ]; then
      echo "Database already exists, skipping bootstrap..."
      exit 0
    fi

    echo "Database is empty, downloading Mithril snapshot..."

    # Set environment variables for mithril-client
    export AGGREGATOR_ENDPOINT="${MITHRIL_AGGREGATOR_ENDPOINT}"
    export GENESIS_VERIFICATION_KEY="${MITHRIL_GENESIS_VERIFICATION_KEY}"


    # Download latest snapshot directly to database directory
    echo "Downloading latest Cardano DB snapshot..."
    /app/bin/mithril-client cardano-db download latest --download-dir /data/db

    # Verify download success
    if [ -d "/data/db/immutable" ]; then
      echo "Mithril bootstrap completed successfully!"
      echo "Database size: $(du -sh /data/db | cut -f1)"
    else
      echo "ERROR: Mithril bootstrap failed"
      exit 1
    fi