apiVersion: v1
kind: ConfigMap
metadata:
  name: mithril-config
data:
  mithril-bootstrap.sh: |
    #!/bin/bash
    set -euo pipefail

    echo "Starting Mithril bootstrap process..."

    # Check if Mithril bootstrap is enabled
    if [ "${ENABLE_MITHRIL_BOOTSTRAP:-false}" != "true" ]; then
      echo "Mithril bootstrap disabled, skipping..."
      exit 0
    fi

    # Check if database already exists and has data
    if [ -d "/data/db/immutable" ] && [ "$(ls -A /data/db/immutable 2>/dev/null)" ]; then
      echo "Database already exists with data, skipping Mithril bootstrap..."
      exit 0
    fi

    echo "Database is empty, proceeding with Mithril bootstrap..."

    # Create necessary directories
    mkdir -p /data/db
    mkdir -p /tmp/mithril-download

    # Set Mithril environment variables based on latest documentation
    export AGGREGATOR_ENDPOINT="${MITHRIL_AGGREGATOR_ENDPOINT}"
    export GENESIS_VERIFICATION_KEY="${MITHRIL_GENESIS_VERIFICATION_KEY}"

    echo "Mithril client configuration:"
    echo "  Aggregator endpoint: ${AGGREGATOR_ENDPOINT}"
    echo "  Network: ${NETWORK}"
    echo "  Snapshot digest: ${MITHRIL_SNAPSHOT_DIGEST}"

    # Get the latest snapshot information using new command structure
    echo "Listing available Cardano DB snapshots..."
    if [ "${MITHRIL_SNAPSHOT_DIGEST}" = "latest" ]; then
      echo "Getting latest snapshot digest..."
      SNAPSHOT_INFO=$(mithril-client cardano-db snapshot list --json | jq -r '.[0]')
      SNAPSHOT_DIGEST=$(echo "${SNAPSHOT_INFO}" | jq -r '.digest')
      SNAPSHOT_SIZE=$(echo "${SNAPSHOT_INFO}" | jq -r '.size // "unknown"')
      SNAPSHOT_EPOCH=$(echo "${SNAPSHOT_INFO}" | jq -r '.beacon.epoch // "unknown"')

      if [ -z "${SNAPSHOT_DIGEST}" ] || [ "${SNAPSHOT_DIGEST}" = "null" ]; then
        echo "ERROR: Failed to get latest snapshot digest"
        exit 1
      fi
    else
      SNAPSHOT_DIGEST="${MITHRIL_SNAPSHOT_DIGEST}"
      echo "Using specified snapshot digest: ${SNAPSHOT_DIGEST}"
      # Get info for specified digest
      SNAPSHOT_INFO=$(mithril-client cardano-db snapshot show ${SNAPSHOT_DIGEST} --json)
      SNAPSHOT_SIZE=$(echo "${SNAPSHOT_INFO}" | jq -r '.size // "unknown"')
      SNAPSHOT_EPOCH=$(echo "${SNAPSHOT_INFO}" | jq -r '.beacon.epoch // "unknown"')
    fi

    echo "Selected snapshot:"
    echo "  Digest: ${SNAPSHOT_DIGEST}"
    echo "  Size: ${SNAPSHOT_SIZE:-unknown}"
    echo "  Epoch: ${SNAPSHOT_EPOCH:-unknown}"

    # Download the snapshot using new command structure
    echo "Downloading Cardano DB snapshot..."
    mithril-client cardano-db download \
      --download-dir /tmp/mithril-download \
      --genesis-verification-key "${GENESIS_VERIFICATION_KEY}" \
      ${SNAPSHOT_DIGEST}

    # Check if download was successful
    if [ ! -d "/tmp/mithril-download" ] || [ -z "$(ls -A /tmp/mithril-download 2>/dev/null)" ]; then
      echo "ERROR: Download failed or download directory is empty"
      exit 1
    fi

    # Move the downloaded data to the target directory
    echo "Moving downloaded data to Cardano node database directory..."

    # Check what was downloaded and move appropriately
    echo "Contents of download directory:"
    ls -la /tmp/mithril-download/

    # The mithril-client downloads directly to the specified directory structure
    # Move immutable files if they exist
    if [ -d "/tmp/mithril-download/immutable" ]; then
      echo "Moving immutable database files..."
      mv /tmp/mithril-download/immutable /data/db/
    fi

    # Move ledger files if they exist
    if [ -d "/tmp/mithril-download/ledger" ]; then
      echo "Moving ledger files..."
      mv /tmp/mithril-download/ledger /data/db/
    fi

    # Move protocolMagicId file if it exists
    if [ -f "/tmp/mithril-download/protocolMagicId" ]; then
      echo "Moving protocolMagicId file..."
      mv /tmp/mithril-download/protocolMagicId /data/db/
    fi

    # Move any other files or directories
    for item in /tmp/mithril-download/*; do
      if [ -e "$item" ]; then
        echo "Moving $(basename "$item")..."
        mv "$item" /data/db/
      fi
    done

    # Verify the database structure
    echo "Verifying database structure:"
    if [ -d "/data/db/immutable" ]; then
      IMMUTABLE_FILES=$(find /data/db/immutable -name "*.chunk" | wc -l)
      echo "  Immutable files: ${IMMUTABLE_FILES}"
    fi

    if [ -d "/data/db/ledger" ]; then
      LEDGER_FILES=$(find /data/db/ledger -type f | wc -l)
      echo "  Ledger files: ${LEDGER_FILES}"
    fi

    DATABASE_SIZE=$(du -sh /data/db | cut -f1)
    echo "  Total database size: ${DATABASE_SIZE}"

    # Clean up temporary files
    rm -rf /tmp/mithril-download

    echo "Mithril bootstrap completed successfully!"
    echo "Cardano node database is ready with verified blockchain data."

  mithril-env-check.sh: |
    #!/bin/bash
    # Environment validation script for Mithril client
    set -euo pipefail

    echo "Validating Mithril environment variables..."

    # Check required environment variables
    if [ -z "${AGGREGATOR_ENDPOINT:-}" ]; then
      echo "ERROR: AGGREGATOR_ENDPOINT is not set"
      exit 1
    fi

    if [ -z "${GENESIS_VERIFICATION_KEY:-}" ]; then
      echo "ERROR: GENESIS_VERIFICATION_KEY is not set"
      exit 1
    fi

    if [ -z "${NETWORK:-}" ]; then
      echo "ERROR: NETWORK is not set"
      exit 1
    fi

    echo "Environment validation passed!"
    echo "  AGGREGATOR_ENDPOINT: ${AGGREGATOR_ENDPOINT}"
    echo "  NETWORK: ${NETWORK}"
    echo "  GENESIS_VERIFICATION_KEY: [REDACTED - ${#GENESIS_VERIFICATION_KEY} chars]"
