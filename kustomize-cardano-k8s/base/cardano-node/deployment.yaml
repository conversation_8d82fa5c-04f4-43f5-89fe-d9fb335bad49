apiVersion: apps/v1
kind: Deployment
metadata:
  name: cardano-node
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cardano-node
  template:
    metadata:
      labels:
        app: cardano-node
    spec:
      initContainers:
      # Mithril bootstrap container for fast initial sync
      - name: mithril-bootstrap
        image: ghcr.io/input-output-hk/mithril-client:latest
        env:
        - name: NETWORK
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: NETWORK
        - name: MITHRIL_AGGREGATOR_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: MITHRIL_AGGREGATOR_ENDPOINT
        - name: MITHRIL_SNAPSHOT_DIGEST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: MITHRIL_SNAPSHOT_DIGEST
        - name: ENABLE_MITHRIL_BOOTSTRAP
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: ENABLE_MITHRIL_BOOTSTRAP
        command: ["/bin/bash"]
        args: ["/scripts/mithril-bootstrap.sh"]
        volumeMounts:
        - name: node-db
          mountPath: /data/db
        - name: mithril-scripts
          mountPath: /scripts
        - name: mithril-snapshot-cache
          mountPath: /shared
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
      containers:
      - name: cardano-node
        image: ghcr.io/intersectmbo/cardano-node:10.1.4
        env:
        - name: NETWORK
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: NETWORK
        - name: CARDANO_NODE_SOCKET_PATH
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_PATH
        ports:
        - containerPort: 3001
          name: cardano-node
          protocol: TCP
        - containerPort: 12788
          name: ekg-metrics
          protocol: TCP
        - containerPort: 30000
          name: socket-tcp
          protocol: TCP
        volumeMounts:
        - name: node-db
          mountPath: /data/db
        - name: node-ipc
          mountPath: /ipc
        - name: mithril-snapshot-cache
          mountPath: /shared
        resources:
          requests:
            memory: "4Gi"
            cpu: "2000m"
          limits:
            memory: "8Gi"
            cpu: "4000m"
        # Health checks for better autoscaling decisions
        # livenessProbe:
        #   httpGet:
        #     path: /
        #     port: 12788
        #   initialDelaySeconds: 60
        #   periodSeconds: 30
        #   timeoutSeconds: 10
        #   failureThreshold: 3
        # readinessProbe:
        #   httpGet:
        #     path: /
        #     port: 12788
        #   initialDelaySeconds: 30
        #   periodSeconds: 10
        #   timeoutSeconds: 5
        #   failureThreshold: 3
      # Socat TCP server container to expose Unix socket over TCP
      - name: socat-tcp-server
        image: alpine/socat
        env:
        - name: PORT
          value: "30000"
        command: ["sh", "-c", "socat TCP-LISTEN:${PORT},fork UNIX-CLIENT:/ipc/node.socket,ignoreeof"]
        ports:
        - containerPort: 30000
          name: socket-tcp
          protocol: TCP
        volumeMounts:
        - name: node-ipc
          mountPath: /ipc
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "200m"
      volumes:
      - name: node-ipc
        emptyDir: {}
      # Use emptyDir for node database (each pod gets fresh copy from shared cache)
      - name: node-db
        emptyDir:
          sizeLimit: 50Gi
      # Mithril bootstrap scripts
      - name: mithril-scripts
        configMap:
          name: mithril-config
          defaultMode: 0755
      # Shared volume for Mithril snapshot caching
      - name: mithril-snapshot-cache
        persistentVolumeClaim:
          claimName: mithril-snapshot-cache
