apiVersion: apps/v1
kind: Deployment
metadata:
  name: cardano-node
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cardano-node
  template:
    metadata:
      labels:
        app: cardano-node
    spec:
      containers:
      - name: cardano-node
        image: ghcr.io/intersectmbo/cardano-node:10.1.4
        env:
        - name: NETWORK
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: NETWORK
        - name: CARDANO_NODE_SOCKET_PATH
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_PATH
        ports:
        - containerPort: 3001
          name: cardano-node
          protocol: TCP
        - containerPort: 12788
          name: ekg-metrics
          protocol: TCP
        - containerPort: 30000
          name: socket-tcp
          protocol: TCP
        volumeMounts:
        - name: node-db
          mountPath: /data/db
        - name: node-ipc
          mountPath: /ipc
        resources:
          requests:
            memory: "4Gi"
            cpu: "1000m"
          limits:
            memory: "8Gi"
            cpu: "4000m"
        # Health checks for better autoscaling decisions
        # livenessProbe:
        #   httpGet:
        #     path: /
        #     port: 12788
        #   initialDelaySeconds: 60
        #   periodSeconds: 30
        #   timeoutSeconds: 10
        #   failureThreshold: 3
        # readinessProbe:
        #   httpGet:
        #     path: /
        #     port: 12788
        #   initialDelaySeconds: 30
        #   periodSeconds: 10
        #   timeoutSeconds: 5
        #   failureThreshold: 3
      # Socat TCP server container to expose Unix socket over TCP
      - name: socat-tcp-server
        image: alpine/socat
        env:
        - name: PORT
          value: "30000"
        command: ["sh", "-c", "socat TCP-LISTEN:${PORT},fork UNIX-CLIENT:/ipc/node.socket,ignoreeof"]
        ports:
        - containerPort: 30000
          name: socket-tcp
          protocol: TCP
        volumeMounts:
        - name: node-ipc
          mountPath: /ipc
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "200m"
      volumes:
      - name: node-ipc
        emptyDir: {}
      # Use emptyDir for stateless operation to enable horizontal scaling
      # In production, consider using shared storage (ReadWriteMany) if blockchain data persistence is needed
      - name: node-db
        emptyDir:
          sizeLimit: 50Gi
