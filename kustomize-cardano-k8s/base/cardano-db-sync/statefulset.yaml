apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: cardano-db-sync
spec:
  serviceName: cardano-db-sync
  replicas: 1
  selector:
    matchLabels:
      app: cardano-db-sync
  template:
    metadata:
      labels:
        app: cardano-db-sync
    spec:
      initContainers:
      # Wait for dependencies: PostgreSQL and Cardano Node
      - name: wait-for-dependencies
        image: postgres:17.2-alpine
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_HOST
        command: ["/bin/bash"]
        args: ["/scripts/wait-for-services.sh"]
        volumeMounts:
        - name: cardano-db-sync-config
          mountPath: /scripts
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      # Clean up any lost+found directories
      - name: cleanup-volumes
        image: busybox:1.36
        command: ["sh", "-c", "rm -rf /var/lib/cexplorer/lost+found /node-ipc/lost+found || true"]
        volumeMounts:
        - name: db-sync-data
          mountPath: /var/lib/cexplorer
        - name: node-ipc
          mountPath: /node-ipc
      containers:
      - name: cardano-db-sync
        image: ghcr.io/intersectmbo/cardano-db-sync:********
        env:
        - name: NETWORK
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: NETWORK
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_PASSWORD
        - name: CARDANO_NODE_SOCKET_PATH
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_PATH
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_HOST
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_PORT
        - name: SOCAT_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: SOCAT_TIMEOUT
        volumeMounts:
        - name: db-sync-data
          mountPath: /var/lib/cexplorer
        - name: node-ipc
          mountPath: /node-ipc
        resources:
          requests:
            memory: "4Gi"
            cpu: "1"
          limits:
            memory: "8Gi"
            cpu: "4"
      # Socat socket client container to connect to cardano-node TCP socket
      - name: socat-socket-client
        image: alpine/socat
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_HOST
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_PORT
        - name: SOCAT_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: SOCAT_TIMEOUT
        command: ["sh", "-c", "rm -f /node-ipc/node.socket; socat UNIX-LISTEN:/node-ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof"]
        volumeMounts:
        - name: node-ipc
          mountPath: /node-ipc
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "200m"
      volumes:
      - name: node-ipc
        emptyDir: {}
      # cardano-db-sync configuration including dependency check scripts
      - name: cardano-db-sync-config
        configMap:
          name: cardano-db-sync-config
          defaultMode: 0755
  volumeClaimTemplates:
  - metadata:
      name: db-sync-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 4Gi
