apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: cardano-db-sync
  labels:
    app.kubernetes.io/component: cardano-db-sync
    app.kubernetes.io/part-of: cardano-stack
spec:
  serviceName: cardano-db-sync
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: cardano-db-sync
      app.kubernetes.io/part-of: cardano-stack
  template:
    metadata:
      labels:
        app.kubernetes.io/component: cardano-db-sync
        app.kubernetes.io/part-of: cardano-stack
    spec:
      initContainers:
      # Clean up any lost+found directories
      - name: cleanup-volumes
        image: busybox:1.36
        command: ["sh", "-c", "rm -rf /var/lib/cexplorer/lost+found /node-ipc/lost+found || true"]
        volumeMounts:
        - name: db-sync-data
          mountPath: /var/lib/cexplorer
        - name: node-ipc
          mountPath: /node-ipc
      containers:
      - name: cardano-db-sync
        image: ghcr.io/intersectmbo/cardano-db-sync:********
        env:
        - name: NETWORK
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: NETWORK
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_PASSWORD
        - name: CARDANO_NODE_SOCKET_PATH
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_PATH
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_HOST
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_PORT
        - name: SOCAT_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: SOCAT_TIMEOUT
        volumeMounts:
        - name: db-sync-data
          mountPath: /var/lib/cexplorer
        - name: node-ipc
          mountPath: /node-ipc
        resources:
          requests:
            memory: "4Gi"
            cpu: "1"
          limits:
            memory: "8Gi"
            cpu: "4"
      # Socat socket client container to connect to cardano-node TCP socket
      - name: socat-socket-client
        image: alpine/socat
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_HOST
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_PORT
        - name: SOCAT_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: SOCAT_TIMEOUT
        command: ["sh", "-c", "rm -f /node-ipc/node.socket; socat UNIX-LISTEN:/node-ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof"]
        volumeMounts:
        - name: node-ipc
          mountPath: /node-ipc
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "200m"
      volumes:
      - name: node-ipc
        emptyDir: {}
  volumeClaimTemplates:
  - metadata:
      name: db-sync-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 4Gi
