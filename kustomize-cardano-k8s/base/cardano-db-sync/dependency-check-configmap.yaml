apiVersion: v1
kind: ConfigMap
metadata:
  name: dependency-check-scripts
data:
  wait-for-services.sh: |
    #!/bin/bash
    set -euo pipefail

    echo "Installing curl..."
    apk add --no-cache curl

    echo "Waiting for dependencies: PostgreSQL and Cardano Node..."

    # Wait for PostgreSQL
    echo "Checking PostgreSQL..."
    until pg_isready -h ${POSTGRES_HOST} -p ${POSTGRES_PORT} -U ${POSTGRES_USER} >/dev/null 2>&1; do
      echo "PostgreSQL not ready, waiting 5 seconds..."
      sleep 5
    done
    echo "PostgreSQL is ready!"

    # Wait for Cardano Node
    echo "Checking Cardano Node..."
    until curl -f -s http://${CARDANO_NODE_SOCKET_TCP_HOST}:12788/ >/dev/null 2>&1; do
      echo "Cardano Node not ready, waiting 10 seconds..."
      sleep 10
    done
    echo "Cardano Node is ready!"

    echo "All dependencies ready - cardano-db-sync can start!"
