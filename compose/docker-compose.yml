# Not recommended for production use, and should only be used for local development!
# This is only a template, feel free to tweak this to fit your needs.
name: cardano-db-sync

services:
  postgres:
    image: postgres:17.2-alpine
    environment:
      - POSTGRES_LOGGING=true
      - POSTGRES_DB=cexplorer
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=v8hlDV0yMAHHlIurYupj
    networks:
      - postgres
    ports:
      - 5432:5432
    volumes:
      - postgres:/var/lib/postgresql/data
    restart: on-failure
    healthcheck:
      # Use pg_isready to check postgres is running. Substitute different
      # user `postgres` if you've setup differently to config/pgpass-mainnet
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    command:
      - -c
      - maintenance_work_mem=1GB
      - -c
      - max_parallel_maintenance_workers=4
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"

  cardano-node:
    image: ghcr.io/intersectmbo/cardano-node:10.1.4
    environment:
      - NETWORK=${NETWORK:-mainnet}
    networks:
      - cardano-node
    volumes:
      - node-db:/data/db
      - node-ipc:/ipc
    restart: on-failure
    healthcheck:
      # Ping the EKG port to see if it responds.
      # Assuming if EKG isn't up then the rest of cardano-node isn't either.
      test: ["CMD-SHELL", "curl -f 127.0.0.1:12788 || exit 1"]
      interval: 60s
      timeout: 10s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"

  cardano-db-sync:
    image: ghcr.io/intersectmbo/cardano-db-sync:********
    environment:
      - NETWORK=${NETWORK:-mainnet}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=cexplorer
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=v8hlDV0yMAHHlIurYupj
    depends_on:
      # Depend on both services to be healthy before starting.
      cardano-node:
        condition: service_healthy
      postgres:
        condition: service_healthy
    networks:
      - cardano-node
      - postgres
    volumes:
      - db-sync-data:/var/lib/cexplorer
      - node-ipc:/node-ipc
    restart: on-failure
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"

volumes:
  volumes:
  db-sync-data:
  postgres:
  node-db:
  node-ipc:

networks:
  cardano-node: {}
  postgres: {}